class Autogenerated {
  bool? success;
  String? message;
  Data? data;

  Autogenerated({this.success, this.message, this.data});

  Autogenerated.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['message'] = this.message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  int? id;
  String? name;
  String? description;
  String? media;
  String? startTime;
  String? endTime;
  int? price;
  User? user;
  List<dynamic>? interestedUsers;
  List<dynamic>? products;
  List<dynamic>? comments;

  Data(
      {this.id,
      this.name,
      this.description,
      this.media,
      this.startTime,
      this.endTime,
      this.price,
      this.user,
      this.interestedUsers,
      this.products,
      this.comments});

  Data.fromJson(Map<String, dynamic> json) {
    try {
      id = json['id'] is int ? json['id'] : (json['id'] is String ? int.tryParse(json['id']) : null);
      name = json['name']?.toString();
      description = json['description']?.toString();
      media = json['media']?.toString();
      startTime = json['startTime']?.toString();
      endTime = json['endTime']?.toString();

      // Handle price field - it might be int, double, or string
      if (json['price'] != null) {
        if (json['price'] is int) {
          price = json['price'];
        } else if (json['price'] is double) {
          price = json['price'].toInt();
        } else if (json['price'] is String) {
          price = int.tryParse(json['price']) ?? 0;
        } else {
          price = 0;
        }
      }

      user = json['user'] != null ? User.fromJson(json['user']) : null;

      interestedUsers = json['interestedUsers'] != null
          ? List<dynamic>.from(json['interestedUsers'])
          : [];

      products = json['products'] != null
          ? List<dynamic>.from(json['products'])
          : [];

      comments = json['comments'] != null
          ? List<dynamic>.from(json['comments'])
          : [];

    } catch (e) {
      print('Error parsing Data from JSON: $e');
      print('JSON data: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['description'] = this.description;
    data['media'] = this.media;
    data['startTime'] = this.startTime;
    data['endTime'] = this.endTime;
    data['price'] = this.price;
    if (this.user != null) {
      data['user'] = this.user!.toJson();
    }
    if (this.interestedUsers != null) {
      data['interestedUsers'] = this.interestedUsers;
    }
    if (this.products != null) {
      data['products'] = this.products;
    }
    if (this.comments != null) {
      data['comments'] = this.comments;
    }
    return data;
  }
}

class User {
  String? username;
  String? email;
  String? firstName;
  String? lastName;
  bool? verified;
  dynamic otp;
  String? otpExpiration;
  dynamic resetOtp;
  String? resetOtpExpiration;
  dynamic profile;
  int? loyaltyPoints;
  int? walletBalance;
  int? reservedBalance;

  User(
      {this.username,
      this.email,
      this.firstName,
      this.lastName,
      this.verified,
      this.otp,
      this.otpExpiration,
      this.resetOtp,
      this.resetOtpExpiration,
      this.profile,
      this.loyaltyPoints,
      this.walletBalance,
      this.reservedBalance});

  User.fromJson(Map<String, dynamic> json) {
    try {
      username = json['username']?.toString();
      email = json['email']?.toString();
      firstName = json['firstName']?.toString();
      lastName = json['lastName']?.toString();
      verified = json['verified'] is bool ? json['verified'] : false;
      otp = json['otp'];
      otpExpiration = json['otpExpiration']?.toString();
      resetOtp = json['resetOtp'];
      resetOtpExpiration = json['resetOtpExpiration']?.toString();
      profile = json['profile'];

      // Handle numeric fields safely
      loyaltyPoints = json['loyaltyPoints'] is int
          ? json['loyaltyPoints']
          : (json['loyaltyPoints'] is String
              ? int.tryParse(json['loyaltyPoints']) ?? 0
              : null);

      walletBalance = json['walletBalance'] is int
          ? json['walletBalance']
          : (json['walletBalance'] is String
              ? int.tryParse(json['walletBalance']) ?? 0
              : null);

      reservedBalance = json['reservedBalance'] is int
          ? json['reservedBalance']
          : (json['reservedBalance'] is String
              ? int.tryParse(json['reservedBalance']) ?? 0
              : null);

    } catch (e) {
      print('Error parsing User from JSON: $e');
      print('JSON data: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['username'] = this.username;
    data['email'] = this.email;
    data['firstName'] = this.firstName;
    data['lastName'] = this.lastName;
    data['verified'] = this.verified;
    data['otp'] = this.otp;
    data['otpExpiration'] = this.otpExpiration;
    data['resetOtp'] = this.resetOtp;
    data['resetOtpExpiration'] = this.resetOtpExpiration;
    data['profile'] = this.profile;
    data['loyaltyPoints'] = this.loyaltyPoints;
    data['walletBalance'] = this.walletBalance;
    data['reservedBalance'] = this.reservedBalance;
    return data;
  }
}
