class Autogenerated {
  bool? success;
  String? message;
  Data? data;

  Autogenerated({this.success, this.message, this.data});

  Autogenerated.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['message'] = this.message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  int? id;
  String? name;
  String? description;
  String? media;
  String? startTime;
  String? endTime;
  int? price;
  User? user;
  List<dynamic>? interestedUsers;
  List<dynamic>? products;
  List<dynamic>? comments;

  Data(
      {this.id,
      this.name,
      this.description,
      this.media,
      this.startTime,
      this.endTime,
      this.price,
      this.user,
      this.interestedUsers,
      this.products,
      this.comments});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    media = json['media'];
    startTime = json['startTime'];
    endTime = json['endTime'];
    price = json['price'];
    user = json['user'] != null ? new User.fromJson(json['user']) : null;
    if (json['interestedUsers'] != null) {
      interestedUsers = List<dynamic>.from(json['interestedUsers']);
    }
    if (json['products'] != null) {
      products = List<dynamic>.from(json['products']);
    }
    if (json['comments'] != null) {
      comments = List<dynamic>.from(json['comments']);
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['description'] = this.description;
    data['media'] = this.media;
    data['startTime'] = this.startTime;
    data['endTime'] = this.endTime;
    data['price'] = this.price;
    if (this.user != null) {
      data['user'] = this.user!.toJson();
    }
    if (this.interestedUsers != null) {
      data['interestedUsers'] = this.interestedUsers;
    }
    if (this.products != null) {
      data['products'] = this.products;
    }
    if (this.comments != null) {
      data['comments'] = this.comments;
    }
    return data;
  }
}

class User {
  String? username;
  String? email;
  String? firstName;
  String? lastName;
  bool? verified;
  Null otp;
  String? otpExpiration;
  Null resetOtp;
  Null resetOtpExpiration;
  Null profile;
  int? loyaltyPoints;
  int? walletBalance;
  int? reservedBalance;

  User(
      {this.username,
      this.email,
      this.firstName,
      this.lastName,
      this.verified,
      this.otp,
      this.otpExpiration,
      this.resetOtp,
      this.resetOtpExpiration,
      this.profile,
      this.loyaltyPoints,
      this.walletBalance,
      this.reservedBalance});

  User.fromJson(Map<String, dynamic> json) {
    username = json['username'];
    email = json['email'];
    firstName = json['firstName'];
    lastName = json['lastName'];
    verified = json['verified'];
    otp = json['otp'];
    otpExpiration = json['otpExpiration'];
    resetOtp = json['resetOtp'];
    resetOtpExpiration = json['resetOtpExpiration'];
    profile = json['profile'];
    loyaltyPoints = json['loyaltyPoints'];
    walletBalance = json['walletBalance'];
    reservedBalance = json['reservedBalance'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['username'] = this.username;
    data['email'] = this.email;
    data['firstName'] = this.firstName;
    data['lastName'] = this.lastName;
    data['verified'] = this.verified;
    data['otp'] = this.otp;
    data['otpExpiration'] = this.otpExpiration;
    data['resetOtp'] = this.resetOtp;
    data['resetOtpExpiration'] = this.resetOtpExpiration;
    data['profile'] = this.profile;
    data['loyaltyPoints'] = this.loyaltyPoints;
    data['walletBalance'] = this.walletBalance;
    data['reservedBalance'] = this.reservedBalance;
    return data;
  }
}
