import 'dart:io';
import 'package:dio/dio.dart';
import 'package:Herfa/features/auth/data/data_source/local/auth_shared_pref_local_data_source.dart';
import '../models/event_model.dart';
import 'package:Herfa/exceptions.dart';
import '../models/return_event.dart';

class EventRepository {
  final Dio _dio;
  final AuthSharedPrefLocalDataSource _authDataSource;
  final String _baseUrl = 'https://zygotic-marys-herfa-c2dd67a8.koyeb.app';

  EventRepository({
    required Dio dio,
    required AuthSharedPrefLocalDataSource authDataSource,
  })  : _dio = dio,
        _authDataSource = authDataSource;

  Future<String> uploadImage(File imageFile) async {
    try {
      String? token = await _authDataSource.getToken();
      if (token == null) {
        throw UnauthorizedException('No authentication token found.');
      }

      _dio.options.headers['Authorization'] = 'Bearer $token';

      String fileName = imageFile.path.split('/').last;
      FormData formData = FormData.fromMap({
        "file":
            await MultipartFile.fromFile(imageFile.path, filename: fileName),
      });

      final response = await _dio.post('/upload', data: formData);
      return response
          .data['imageUrl']; // Assuming API returns { "imageUrl": "..." }
    } on DioException catch (e) {
      print('DioException during image upload:');
      print('  Response status code: ${e.response?.statusCode}');
      print('  Response data: ${e.response?.data}');
      print('  Error type: ${e.type}');
      print('  Error: ${e.error}');
      if (e.response?.statusCode == 401) {
        throw UnauthorizedException('Unauthorized: Invalid or expired token.');
      } else {
        throw Exception(
            'Failed to upload image: ${e.message ?? 'Unknown network error or server issue.'}');
      }
    } catch (e) {
      throw Exception('Failed to upload image: $e');
    }
  }

  Future<List<Data>> getEvents() async {
    try {
      String? token = await _authDataSource.getToken();
      if (token == null) {
        throw UnauthorizedException('No authentication token found.');
      }

      _dio.options.headers['Authorization'] = 'Bearer $token';

      final response = await _dio.get('$_baseUrl/events');

      // Assume the response.data is always a Map with a 'data' key
      final responseMap = response.data as Map<String, dynamic>;
      if (!responseMap.containsKey('data') ||
          !(responseMap['data'] is List<dynamic>)) {
        throw Exception(
            "Failed to load events: Invalid API response format (missing or invalid 'data' field).");
      }
      final List<dynamic> eventJson = responseMap['data'] as List<dynamic>;

      return eventJson.map((json) => Data.fromJson(json)).toList();
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw UnauthorizedException('Unauthorized: Invalid or expired token.');
      } else {
        throw Exception('Failed to load events: ${e.message}');
      }
    } catch (e) {
      throw Exception('Failed to load events: $e');
    }
  }

  Future<Data> createEvent(EventModel event, File imageFile) async {
    try {
      String? token = await _authDataSource.getToken();
      if (token == null) {
        throw UnauthorizedException('No authentication token found.');
      }

      _dio.options.headers['Authorization'] = 'Bearer $token';

      final imageUrl = await uploadImage(imageFile);
      final eventWithImageUrl = event.copyWith(imageUrl: imageUrl);

      print('Event data before sending: ${eventWithImageUrl.toJson()}');

      final response =
          await _dio.post('$_baseUrl/events', data: eventWithImageUrl.toJson());
      print('Create event response: ${response.data}');
      return Data.fromJson(response.data['data']);
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw UnauthorizedException('Unauthorized: Invalid or expired token.');
      } else {
        throw Exception('Failed to create event: ${e.message}');
      }
    } catch (e) {
      throw Exception('Failed to create event: $e');
    }
  }

  Future<Data> updateEvent(EventModel event) async {
    try {
      String? token = await _authDataSource.getToken();
      if (token == null) {
        throw UnauthorizedException('No authentication token found.');
      }

      _dio.options.headers['Authorization'] = 'Bearer $token';

      final response =
          await _dio.put('$_baseUrl/events/${event.id}', data: event.toJson());
      return Data.fromJson(response.data['data']);
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw UnauthorizedException('Unauthorized: Invalid or expired token.');
      } else {
        throw Exception('Failed to update event: ${e.message}');
      }
    } catch (e) {
      throw Exception('Failed to update event: $e');
    }
  }

  Future<void> deleteEvent(String eventId) async {
    try {
      String? token = await _authDataSource.getToken();
      if (token == null) {
        throw UnauthorizedException('No authentication token found.');
      }

      _dio.options.headers['Authorization'] = 'Bearer $token';

      await _dio.delete('$_baseUrl/events/$eventId');
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw UnauthorizedException('Unauthorized: Invalid or expired token.');
      } else {
        throw Exception('Failed to delete event: ${e.message}');
      }
    } catch (e) {
      throw Exception('Failed to delete event: $e');
    }
  }
}
