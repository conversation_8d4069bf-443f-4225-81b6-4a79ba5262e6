import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:Herfa/features/event/data/models/return_event.dart';
import '../../data/repositories/event_repository.dart';
import 'dart:io';
import 'package:Herfa/features/event/data/models/event_model.dart';

// States
abstract class EventState extends Equatable {
  const EventState();

  @override
  List<Object> get props => [];
}

class EventInitial extends EventState {}

class EventLoading extends EventState {}

class EventLoaded extends EventState {
  final List<Data> events;

  const EventLoaded(this.events);

  @override
  List<Object> get props => [events];
}

class EventError extends EventState {
  final String message;

  const EventError(this.message);

  @override
  List<Object> get props => [message];
}

// Cubit
class EventCubit extends Cubit<EventState> {
  final EventRepository _eventRepository;

  EventCubit(this._eventRepository) : super(EventInitial()) {
    loadEvents(); // Automatically load events when the cubit is created
  }

  Future<void> loadEvents() async {
    try {
      emit(EventLoading());
      final events = await _eventRepository.getEvents();
      emit(EventLoaded(events));
    } catch (e) {
      emit(EventError(e.toString()));
    }
  }

  Future<void> createEvent(EventModel event, File image) async {
    try {
      emit(EventLoading());
      final createdEvent = await _eventRepository.createEvent(event, image);
      final currentState = state;
      if (currentState is EventLoaded) {
        emit(EventLoaded([...currentState.events, createdEvent]));
      } else {
        emit(EventLoaded([createdEvent]));
      }
    } catch (e) {
      emit(EventError(e.toString()));
    }
  }

  Future<void> updateEvent(EventModel event) async {
    try {
      emit(EventLoading());
      await _eventRepository.updateEvent(event);
      await loadEvents(); // Reload events after updating
    } catch (e) {
      emit(EventError(e.toString()));
    }
  }

  Future<void> deleteEvent(String eventId) async {
    try {
      emit(EventLoading());
      await _eventRepository.deleteEvent(eventId);
      await loadEvents(); // Reload events after deleting
    } catch (e) {
      emit(EventError(e.toString()));
    }
  }
}
